# ===== 数据集文件 =====
# 排除所有数据集相关文件和目录
data/*/images/
data/*/annotations/
data/*/labels/
data/*/masks/
data/*/*.json
data/*/*.csv
data/*/*.txt
data/*/*.h5
data/*/*.hdf5
data/*/*.pkl
data/*/*.pickle
data/*/*.npy
data/*/*.npz
data/*/*.mat

# MIMIC-CXR 下载目录
physionet.org/

# 常见医学影像格式
*.dcm
*.dicom
*.nii
*.nii.gz
*.mha
*.mhd
*.raw

# 大型数据文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# ===== Python =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ===== 深度学习相关 =====
# 模型权重和检查点
*.pth
*.pt
*.ckpt
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.engine

# 训练日志和输出
logs/
runs/
outputs/
results/
checkpoints/
saved_models/
experiments/
wandb/
mlruns/

# TensorBoard
events.out.tfevents.*
*.tfevents.*

# PyTorch Lightning
lightning_logs/

# Weights & Biases
wandb/

# MLflow
mlruns/

# ===== IDE和编辑器 =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 系统文件 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== 其他 =====
# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.gz
*.bz2
*.xz

# 配置文件（可能包含敏感信息）
config.yaml
config.yml
secrets.yaml
secrets.yml
.env.local
.env.production

# 缓存目录
cache/
.cache/
tmp/
temp/

# 用户特定的运行时文件
*.pid
*.seed
*.pid.lock

# 可选的npm依赖目录（如果项目中有前端组件）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
